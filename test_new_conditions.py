#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新条件的股票分析脚本
"""

import pandas as pd
import os
from datetime import datetime

def create_test_stock_data():
    """
    创建测试用的股票数据文件
    """
    # 创建一些测试股票数据
    test_data = {
        '完整代码': ['sz.000001', 'sz.000002', 'sh.600000', 'sh.600036'],
        '股票名称': ['平安银行', '万科A', '浦发银行', '招商银行'],
        '第一次涨停前一天': ['2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18']
    }
    
    df = pd.DataFrame(test_data)
    df.to_excel('stock_codes_with_prev_day.xlsx', index=False)
    print("测试股票池文件已创建: stock_codes_with_prev_day.xlsx")
    return df

def main():
    """
    主测试函数
    """
    print("="*60)
    print("测试新条件的股票分析程序")
    print("="*60)
    
    # 检查是否存在股票池文件
    if not os.path.exists('stock_codes_with_prev_day.xlsx'):
        print("未找到股票池文件，创建测试数据...")
        create_test_stock_data()
    
    # 导入并运行主程序
    try:
        from main import main as run_analysis
        print("开始运行分析...")
        run_analysis()
    except Exception as e:
        print(f"运行分析时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

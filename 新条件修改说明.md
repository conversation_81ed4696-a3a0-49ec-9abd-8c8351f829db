# 股票形态识别新条件修改说明

## 最新更新 (2025-01-27)

### 📁 股票池文件更新
- **新股票池**: `stock_pool_20250725.xlsx` (1423只股票)
- **分析方式**: 最新日期往前50个交易日
- **自动日期**: 程序自动计算最新交易日作为分析截止日期

### 🔄 主要变更
1. 修改了 `load_stock_codes()` 函数以读取新的股票池文件
2. 自动计算分析截止日期（避免周末）
3. 适配新的文件格式（code, full_code, name, price, ipo_date, market）
4. 保持所有新条件筛选逻辑不变

## 修改概述

根据您提供的新条件要求，我对 `main.py` 文件进行了全面修改，增加了更严格的筛选条件，确保只有真正符合条件的股票才会被识别和保存。

## 新增条件详解

### 1. 双顶形态新条件

**原条件**: 基本的双顶形态识别
**新增条件**: 
- 条件1：还没有K线突破双顶的价格
- 条件2：有少于3根K线超过了双顶价格，且超过双顶价格的K线是实体很小的十字星型K线
- 两个条件满足一个就算满足

**实现方式**:
- 新增 `is_doji_candle()` 函数识别十字星型K线
- 修改 `detect_double_top()` 函数，增加突破情况检查
- 返回值增加 `meets_new_conditions` 标志

### 2. MACD形态新条件

**原条件**: 基本的MACD背离识别
**新增条件**:
- 两个峰值之间需要有MACD柱接近0的条件
- 最近3日内出现了其中一个峰值点

**实现方式**:
- 修改 `detect_macd_divergence()` 函数
- 检查峰值间是否有接近0的MACD柱（阈值0.01）
- 检查最近3日内是否有峰值点
- 返回值增加参考价格

### 3. KDJ形态新条件

**原条件**: 基本的KDJ金叉信号识别
**新增条件**:
- 最新日期对应的时间是入场点

**实现方式**:
- 修改 `detect_kdj_signals()` 函数
- 只有当信号日期等于最新日期时才认为满足条件
- 返回值增加参考价格

### 4. 参考价格计算

**新规则**:
- MACD形态和KDJ形态：采用信号K的实体区域的底部对应的价格
- 双顶形态：采用双顶的价格

**实现方式**:
- 对于MACD和KDJ：`min(signal_open, signal_close)`
- 对于双顶：`(price1 + price2) / 2`

## 主要代码修改

### 1. 新增函数

```python
def is_doji_candle(open_price, close_price, high_price, low_price, threshold=0.02):
    """判断是否为十字星型K线"""
```

### 2. 修改的函数

1. **detect_double_top()**: 增加突破检查和新条件验证
2. **detect_kdj_signals()**: 增加最新日期检查和参考价格计算
3. **detect_macd_divergence()**: 增加峰值间检查和时间条件验证
4. **analyze_single_stock()**: 适配新的返回值格式
5. **plot_stock_analysis()**: 适配新的参数格式

### 3. 结果保存优化

- **筛选机制**: 只保存满足新条件的股票到Excel文件
- **新增列**: 形态类型、参考价格、各形态的详细参考价格
- **图片保存**: 只为满足新条件的股票生成和保存图片到指定目录

## 输出文件

### 1. Excel文件
- 文件名: `满足新条件的股票分析结果_YYYYMMDD_HHMMSS.xlsx`
- 包含列: 股票代码、形态类型、参考价格、各形态详细信息等

### 2. 图片文件
- 保存路径: `D:\量化投资\股票\盘前检测形态\Fig`
- 文件名: `{股票代码}_analysis.png`
- 只为满足新条件的股票生成

## 运行方式

### 方式一：直接运行主程序
```bash
python main.py
```

### 方式二：使用运行脚本（推荐）
```bash
python run_analysis.py
```

### 文件要求
1. 确保 `stock_pool_20250725.xlsx` 文件存在
2. 程序会自动读取1423只股票进行分析
3. 自动计算最新交易日作为分析截止日期
4. 程序会自动筛选并保存满足新条件的股票

## 统计信息

程序运行完成后会显示：
- 总股票数量
- 成功分析数量  
- 满足新条件的各类形态数量
- 文件保存路径

## 注意事项

1. 所有条件都必须同时满足才会被认为是有效信号
2. 只有满足新条件的股票才会生成图表和保存到结果文件
3. 参考价格按照指定规则计算，便于后续交易决策
4. 程序增加了详细的调试信息，便于跟踪分析过程

## 文件结构说明

### 输入文件
- `stock_pool_20250725.xlsx`: 股票池文件
  - 列名: code, full_code, name, price, ipo_date, market
  - 数量: 1423只股票
  - 包含沪深两市主要股票

### 输出文件
- Excel: `满足新条件的股票分析结果_YYYYMMDD_HHMMSS.xlsx`
- 图片: `D:\量化投资\股票\盘前检测形态\Fig\{股票代码}_analysis.png`

## 测试建议

建议使用提供的测试脚本进行验证：
```bash
python simple_pool_test.py  # 基础功能测试
python run_analysis.py      # 完整分析运行
```

确认项目：
1. 新条件逻辑正确
2. 参考价格计算准确
3. 文件保存路径正确
4. 图表生成正常
5. 股票池文件读取正常

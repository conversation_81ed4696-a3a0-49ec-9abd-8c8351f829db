# 股票形态识别程序使用说明

## 📋 程序概述

本程序已成功修改为读取 `stock_pool_20250725.xlsx` 文件作为股票池，分析最新日期往前50个交易日的数据，并应用严格的新条件筛选，只保存真正符合条件的股票。

## 🎯 核心功能

### 1. 股票池分析
- **股票数量**: 1423只股票（沪深两市）
- **数据范围**: 最新日期往前50个交易日
- **自动日期**: 程序自动计算最新交易日作为分析截止日期

### 2. 形态识别
- **双顶形态**: 识别双顶并检查突破情况
- **KDJ信号**: 识别金叉信号并验证时效性
- **MACD背离**: 识别背离信号并检查峰值条件

### 3. 新条件筛选
- **双顶**: 无突破或少于3根十字星突破
- **MACD**: 峰值间有接近0的柱且最近3日有峰值
- **KDJ**: 最新日期是入场点
- **参考价格**: 精确计算各形态的参考价格

## 🚀 运行方法

### 方式一：直接运行（推荐）
```bash
python run_analysis.py
```

### 方式二：运行主程序
```bash
python main.py
```

### 方式三：验证代码
```bash
python verify_changes.py
```

## 📁 文件说明

### 输入文件
- `stock_pool_20250725.xlsx`: 股票池文件（必需）
- `main.py`: 主程序文件

### 输出文件
- `满足新条件的股票分析结果_YYYYMMDD_HHMMSS.xlsx`: 筛选结果
- `D:\量化投资\股票\盘前检测形态\Fig\{股票代码}_analysis.png`: 技术分析图表

### 辅助文件
- `run_analysis.py`: 运行脚本
- `verify_changes.py`: 验证脚本
- `新条件修改说明.md`: 详细修改说明

## 📊 输出结果说明

### Excel文件列说明
- **股票代码**: 完整股票代码（如sh.600000）
- **形态类型**: 满足条件的形态类型
- **参考价格**: 各形态的参考价格
- **双顶信息**: 双顶形态的详细信息
- **KDJ信息**: KDJ信号的详细信息
- **MACD信息**: MACD背离的详细信息

### 图表文件说明
- 只为满足新条件的股票生成图表
- 包含K线图、MACD、KDJ、成交量四个子图
- 标记所有识别的形态和信号点
- 高分辨率PNG格式，便于查看

## ⚙️ 新条件详解

### 1. 双顶形态新条件
```
条件1：还没有K线突破双顶的价格
条件2：有少于3根K线超过了双顶价格，且超过双顶价格的K线是实体很小的十字星型K线
满足其中一个条件即可
```

### 2. MACD形态新条件
```
条件1：两个峰值之间需要有MACD柱接近0的条件
条件2：最近3日内出现了其中一个峰值点
必须同时满足两个条件
```

### 3. KDJ形态新条件
```
条件：最新日期对应的时间是入场点
确保信号的时效性
```

### 4. 参考价格规则
```
MACD形态：信号K的实体区域的底部对应的价格
KDJ形态：信号K的实体区域的底部对应的价格
双顶形态：双顶的价格（连接线价格）
```

## 🔍 运行示例

```bash
# 1. 验证代码修改
python verify_changes.py

# 2. 运行完整分析
python run_analysis.py

# 3. 查看结果
# Excel文件：满足新条件的股票分析结果_20250127_143022.xlsx
# 图片文件：D:\量化投资\股票\盘前检测形态\Fig\sh.600000_analysis.png
```

## ⚠️ 注意事项

1. **网络连接**: 程序需要连接baostock获取股票数据
2. **运行时间**: 1423只股票分析需要较长时间，请耐心等待
3. **磁盘空间**: 确保有足够空间保存图片文件
4. **权限**: 确保有权限创建输出目录

## 🛠️ 故障排除

### 常见问题
1. **文件不存在**: 确保 `stock_pool_20250725.xlsx` 在当前目录
2. **网络错误**: 检查网络连接，baostock可能需要重试
3. **权限错误**: 确保有权限创建 `D:\量化投资\股票\盘前检测形态\Fig` 目录
4. **内存不足**: 大量股票分析可能需要较多内存

### 调试方法
```bash
# 运行验证脚本检查配置
python verify_changes.py

# 查看详细错误信息
python main.py 2>&1 | tee analysis.log
```

## 📈 预期结果

- 从1423只股票中筛选出真正符合新条件的股票
- 每只符合条件的股票都有详细的技术分析图表
- 精确的参考价格便于制定交易策略
- 完整的Excel报告便于进一步分析

---

**最后更新**: 2025-01-27  
**版本**: v2.0 (支持新股票池和新条件筛选)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试新条件功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 导入主程序的函数
from main import (
    is_doji_candle, 
    detect_double_top, 
    detect_kdj_signals, 
    detect_macd_divergence,
    calculate_macd,
    calculate_kdj
)

def create_test_data():
    """
    创建测试数据
    """
    dates = []
    base_date = datetime(2024, 1, 1)
    for i in range(50):
        dates.append((base_date + timedelta(days=i)).strftime('%Y-%m-%d'))
    
    # 创建模拟股票数据
    np.random.seed(42)
    prices = 10 + np.cumsum(np.random.randn(50) * 0.1)
    
    data = pd.DataFrame({
        'date': dates,
        'open': prices + np.random.randn(50) * 0.05,
        'high': prices + np.abs(np.random.randn(50) * 0.1),
        'low': prices - np.abs(np.random.randn(50) * 0.1),
        'close': prices + np.random.randn(50) * 0.05,
        'volume': np.random.randint(1000, 10000, 50)
    })
    
    return data

def test_doji_candle():
    """
    测试十字星识别
    """
    print("测试十字星识别...")
    
    # 测试十字星
    is_doji1 = is_doji_candle(10.0, 10.05, 10.5, 9.8)  # 小实体
    is_doji2 = is_doji_candle(10.0, 10.3, 10.5, 9.8)   # 大实体
    
    print(f"小实体K线是否为十字星: {is_doji1}")
    print(f"大实体K线是否为十字星: {is_doji2}")

def test_double_top():
    """
    测试双顶识别
    """
    print("\n测试双顶识别...")
    
    data = create_test_data()
    result = detect_double_top(data)
    
    print(f"双顶识别结果: {result}")

def test_kdj_signals():
    """
    测试KDJ信号识别
    """
    print("\n测试KDJ信号识别...")
    
    data = create_test_data()
    result = detect_kdj_signals(data)
    
    print(f"KDJ信号识别结果: {result}")

def test_macd_divergence():
    """
    测试MACD背离识别
    """
    print("\n测试MACD背离识别...")
    
    data = create_test_data()
    result = detect_macd_divergence(data)
    
    print(f"MACD背离识别结果: {result}")

def main():
    """
    主测试函数
    """
    print("="*50)
    print("新条件功能测试")
    print("="*50)
    
    try:
        test_doji_candle()
        test_double_top()
        test_kdj_signals()
        test_macd_divergence()
        
        print("\n="*50)
        print("所有测试完成！")
        print("="*50)
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
